"""Command to process document chunks and generate statements."""

from langfuse.decorators import observe

from accessor.document.document_accessor import DocumentAccessor
from engine.synthesis.synthesis_engine import SynthesisEngine
from manager.feed.commands.base_workflow_processor import BaseWorkflowProcessor
from manager.feed.commands.dependency_factory import DependencyFactory
from shared.enums.job_enums import JobItemTypeEnum
from shared.models.workflow_type_enum import WorkflowTypeEnum
from util.config import AWS_SNS_JOB_ARN
from util.langfuse.decorators import trace_context
from util.logging_config import setup_logging

MAX_WORKERS = 5  # Configurable thread pool size

class ProcessDocumentChunksCreatedCommand(BaseWorkflowProcessor):
    """Command to process document chunks and generate statements."""

    @property
    def workflow_type(self) -> WorkflowTypeEnum:
        return WorkflowTypeEnum.GENERATE_STATEMENTS

    def __init__(
        self,
        dependency_factory: DependencyFactory,
        document_id: int,
        job_id: int,
        receipt_handle: str,
        sqs_client,
        sns_client,
        logger=None,
        chunk_id=None,
        domain_id=None,
    ):
        super().__init__(
            dependency_factory,
            document_id,
            job_id,
            receipt_handle,
            sqs_client,
            sns_client,
            logger,
        )
        self.synthesis_engine = self.dependency_factory.get_synthesis_engine()
        # Cache frequently accessed values
        self._job_type_id = self.document_accessor.get_job_type_id_by_document(self.document_id)
        self._statements_created_topic_arn = self.sns_client.get_topic_arn(
            "phoenix-burst", "document-statements-created"
        )

    @observe(name="generate_document_statements_bulk")
    @trace_context(["document_id", "job_id"])
    def execute(self):
        try:
            super().execute()
        except Exception as e:
            self.logger.exception(
                "Error processing document chunks. Setting job to error status: %s", e
            )
            self.document_accessor.job_error(self.job_id)

    def _seed_item_rows(self) -> None:
        """Seed job item rows for each chunk in the document."""
        chunk_ids = self.document_accessor.get_chunk_ids_for_document(self.document_id)
        self.logger.info(
            "Seeding %d chunk items for document %d",
            len(chunk_ids),
            self.document_id
        )
        self.document_accessor.seed_items(
            self.job_workflow_id,
            [str(cid) for cid in chunk_ids],
            item_type=JobItemTypeEnum.CHUNK
        )

    def process_item(self, item_key: str):
        """Process a single chunk by generating statements.

        Args:
            item_key: The chunk ID to process
        """
        chunk_id = int(item_key)
        self.logger.debug(
            "Generating statements for chunk %d in document %d",
            chunk_id,
            self.document_id,
        )
        self.synthesis_engine.generate_chunk_statements(
            document_id=self.document_id,
            chunk_id=chunk_id,
            job_type_id=self._job_type_id,
        )

    def cleanup_item(self, item_key: str):
        """Clean up a failed chunk.

        Note: Currently a no-op as partial statements are not a concern.
        If a chunk fails during statement generation, no partial statements
        are created that would need cleanup. If this changes in the future,
        this method should be implemented to clean up any partial state.
        """
        pass

    def post_workflow(self):
        """Publish SNS notification after all chunks are processed."""
        self._publish_sns_message(
            self._statements_created_topic_arn,
            {
                "header": {"type": "job-process-document-statements-created"},
                "body": {"document_id": self.document_id, "job_id": self.job_id},
            },
        )
