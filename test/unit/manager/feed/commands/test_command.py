"""Tests for Command base class."""

import json
import unittest
import uuid
from unittest.mock import Mock, <PERSON><PERSON>ock, patch

from manager.feed.commands.command import Command
from manager.feed.commands.dependency_factory import DependencyFactory


class ConcreteCommand(Command):
    """Concrete implementation of Command for testing."""
    
    def execute(self):
        """Concrete implementation of abstract execute method."""
        return "executed"


class TestCommand(unittest.TestCase):
    """Unit tests for Command base class."""

    def setUp(self):
        """Set up test environment."""
        self.mock_dependency_factory = Mock(spec=DependencyFactory)
        self.mock_sqs_client = Mock()
        self.mock_sns_client = Mock()
        self.mock_logger = Mock()
        
        self.document_id = 123
        self.job_id = 456
        self.receipt_handle = "test-receipt-handle"
        
        self.command = ConcreteCommand(
            dependency_factory=self.mock_dependency_factory,
            document_id=self.document_id,
            job_id=self.job_id,
            receipt_handle=self.receipt_handle,
            sqs_client=self.mock_sqs_client,
            sns_client=self.mock_sns_client,
            logger=self.mock_logger,
        )

    def test_constructor_initialization(self):
        """Test that constructor properly initializes all attributes."""
        self.assertEqual(self.command.dependency_factory, self.mock_dependency_factory)
        self.assertEqual(self.command.document_id, self.document_id)
        self.assertEqual(self.command.job_id, self.job_id)
        self.assertEqual(self.command.receipt_handle, self.receipt_handle)
        self.assertEqual(self.command.sqs_client, self.mock_sqs_client)
        self.assertEqual(self.command.sns_client, self.mock_sns_client)
        self.assertEqual(self.command.logger, self.mock_logger)

    def test_max_run_count_constant(self):
        """Test that MAX_RUN_COUNT constant is properly defined."""
        self.assertEqual(Command.MAX_RUN_COUNT, 5)

    def test_abstract_execute_method(self):
        """Test that Command is abstract and execute method must be implemented."""
        # Test that we cannot instantiate Command directly
        with self.assertRaises(TypeError):
            Command(
                dependency_factory=self.mock_dependency_factory,
                document_id=self.document_id,
                job_id=self.job_id,
                receipt_handle=self.receipt_handle,
                sqs_client=self.mock_sqs_client,
                sns_client=self.mock_sns_client,
                logger=self.mock_logger,
            )
        
        # Test that concrete implementation works
        result = self.command.execute()
        self.assertEqual(result, "executed")

    @patch('manager.feed.commands.command.AWS_FEED_MANAGER_QUEUE', 'test-queue-url')
    def test_delete_message_with_default_log(self):
        """Test _delete_message with default log message."""
        test_receipt = "test-receipt-123"
        
        self.command._delete_message(test_receipt)
        
        # Verify SQS delete_message was called with correct parameters
        self.mock_sqs_client.delete_message.assert_called_once_with(
            QueueUrl='test-queue-url',
            ReceiptHandle=test_receipt
        )
        
        # Verify default log message was used
        self.mock_logger.debug.assert_called_once_with(
            f"Deleting message from queue {test_receipt}"
        )

    @patch('manager.feed.commands.command.AWS_FEED_MANAGER_QUEUE', 'test-queue-url')
    def test_delete_message_with_custom_log(self):
        """Test _delete_message with custom log message."""
        test_receipt = "test-receipt-456"
        custom_message = "Custom deletion message"
        
        self.command._delete_message(test_receipt, custom_message)
        
        # Verify SQS delete_message was called with correct parameters
        self.mock_sqs_client.delete_message.assert_called_once_with(
            QueueUrl='test-queue-url',
            ReceiptHandle=test_receipt
        )
        
        # Verify custom log message was used
        self.mock_logger.debug.assert_called_once_with(custom_message)

    @patch('manager.feed.commands.command.uuid.uuid4')
    def test_publish_sns_message_success(self, mock_uuid):
        """Test successful SNS message publishing."""
        # Setup
        mock_uuid.side_effect = [
            Mock(return_value="dedup-id-123"),
            Mock(return_value="group-id-456")
        ]
        mock_uuid.return_value.__str__ = Mock(side_effect=["dedup-id-123", "group-id-456"])
        
        test_topic_arn = "arn:aws:sns:us-east-1:123456789012:test-topic"
        test_message = {"header": {"type": "test"}, "body": {"data": "test"}}
        
        # Mock successful SNS response
        self.mock_sns_client.publish.return_value = {
            "ResponseMetadata": {"HTTPStatusCode": 200},
            "MessageId": "msg-id-789"
        }
        
        # Execute
        result = self.command._publish_sns_message(test_topic_arn, test_message)
        
        # Verify
        self.assertTrue(result)
        
        # Check SNS publish was called with correct parameters
        self.mock_sns_client.publish.assert_called_once()
        call_args = self.mock_sns_client.publish.call_args[1]
        
        self.assertEqual(call_args["TopicArn"], test_topic_arn)
        self.assertEqual(json.loads(call_args["Message"]), test_message)
        self.assertIn("MessageGroupId", call_args)
        self.assertIn("MessageDeduplicationId", call_args)
        
        # Verify success log
        self.mock_logger.info.assert_called_once_with(
            "Message published successfully with MessageId: %s", "msg-id-789"
        )

    def test_publish_sns_message_failure_bad_status(self):
        """Test SNS message publishing with bad HTTP status."""
        test_topic_arn = "arn:aws:sns:us-east-1:123456789012:test-topic"
        test_message = {"header": {"type": "test"}, "body": {"data": "test"}}
        
        # Mock failed SNS response
        failed_response = {
            "ResponseMetadata": {"HTTPStatusCode": 400},
            "MessageId": ""
        }
        self.mock_sns_client.publish.return_value = failed_response
        
        # Execute
        result = self.command._publish_sns_message(test_topic_arn, test_message)
        
        # Verify
        self.assertFalse(result)
        
        # Verify error log
        self.mock_logger.error.assert_called_once_with(
            "Failed to publish message: %s", failed_response
        )

    def test_publish_sns_message_failure_no_message_id(self):
        """Test SNS message publishing with missing MessageId."""
        test_topic_arn = "arn:aws:sns:us-east-1:123456789012:test-topic"
        test_message = {"header": {"type": "test"}, "body": {"data": "test"}}
        
        # Mock SNS response with missing MessageId
        failed_response = {
            "ResponseMetadata": {"HTTPStatusCode": 200},
            "MessageId": ""  # Empty MessageId
        }
        self.mock_sns_client.publish.return_value = failed_response
        
        # Execute
        result = self.command._publish_sns_message(test_topic_arn, test_message)
        
        # Verify
        self.assertFalse(result)
        
        # Verify error log
        self.mock_logger.error.assert_called_once_with(
            "Failed to publish message: %s", failed_response
        )

    def test_publish_sns_message_exception(self):
        """Test SNS message publishing with exception."""
        test_topic_arn = "arn:aws:sns:us-east-1:123456789012:test-topic"
        test_message = {"header": {"type": "test"}, "body": {"data": "test"}}
        
        # Mock SNS client to raise exception
        test_exception = Exception("SNS service unavailable")
        self.mock_sns_client.publish.side_effect = test_exception
        
        # Execute
        result = self.command._publish_sns_message(test_topic_arn, test_message)
        
        # Verify
        self.assertFalse(result)
        
        # Verify exception log
        self.mock_logger.exception.assert_called_once_with(
            "Error when attempting to publish message: %s", test_exception, exc_info=True
        )

    @patch('manager.feed.commands.command.uuid.uuid4')
    def test_publish_sns_message_json_serialization(self, mock_uuid):
        """Test that message is properly JSON serialized."""
        # Setup
        mock_uuid.return_value.__str__ = Mock(return_value="test-uuid")
        
        test_topic_arn = "arn:aws:sns:us-east-1:123456789012:test-topic"
        test_message = {
            "header": {"type": "test", "timestamp": "2023-01-01T00:00:00Z"},
            "body": {"document_id": 123, "job_id": 456, "data": ["item1", "item2"]}
        }
        
        # Mock successful SNS response
        self.mock_sns_client.publish.return_value = {
            "ResponseMetadata": {"HTTPStatusCode": 200},
            "MessageId": "msg-id-123"
        }
        
        # Execute
        result = self.command._publish_sns_message(test_topic_arn, test_message)
        
        # Verify
        self.assertTrue(result)
        
        # Check that message was JSON serialized correctly
        call_args = self.mock_sns_client.publish.call_args[1]
        serialized_message = call_args["Message"]
        
        # Verify it's valid JSON and matches original
        deserialized = json.loads(serialized_message)
        self.assertEqual(deserialized, test_message)

    @patch('manager.feed.commands.command.uuid.uuid4')
    def test_publish_sns_message_uuid_generation(self, mock_uuid):
        """Test that UUIDs are properly generated for deduplication and grouping."""
        # Setup
        mock_dedup_uuid = Mock()
        mock_dedup_uuid.__str__ = Mock(return_value="dedup-uuid-123")
        mock_group_uuid = Mock()
        mock_group_uuid.__str__ = Mock(return_value="group-uuid-456")
        
        mock_uuid.side_effect = [mock_dedup_uuid, mock_group_uuid]
        
        test_topic_arn = "arn:aws:sns:us-east-1:123456789012:test-topic"
        test_message = {"test": "data"}
        
        # Mock successful SNS response
        self.mock_sns_client.publish.return_value = {
            "ResponseMetadata": {"HTTPStatusCode": 200},
            "MessageId": "msg-id-123"
        }
        
        # Execute
        self.command._publish_sns_message(test_topic_arn, test_message)
        
        # Verify UUIDs were generated and used
        self.assertEqual(mock_uuid.call_count, 2)
        
        call_args = self.mock_sns_client.publish.call_args[1]
        self.assertEqual(call_args["MessageDeduplicationId"], "dedup-uuid-123")
        self.assertEqual(call_args["MessageGroupId"], "group-uuid-456")


if __name__ == "__main__":
    unittest.main()
