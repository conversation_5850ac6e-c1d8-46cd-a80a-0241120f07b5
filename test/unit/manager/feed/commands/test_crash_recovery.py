"""Unit tests for crash recovery functionality in workflow processors."""

import pytest
from unittest.mock import Mock, MagicMock, patch
from manager.feed.commands.process_document_chunks_created_command import ProcessDocumentChunksCreatedCommand
from shared.enums.job_enums import JobItemTypeEnum
from shared.models.workflow_type_enum import WorkflowTypeEnum
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum
from shared.models.job_workflow import JobWorkflow


@pytest.fixture
def mock_document_accessor():
    accessor = Mock()
    accessor.get_chunk_ids_for_document.return_value = [1, 2, 3, 4, 5]
    accessor.get_job_type_id_by_document.return_value = 42
    return accessor


@pytest.fixture
def mock_content_accessor():
    return Mock()


@pytest.fixture
def mock_synthesis_engine():
    return Mock()


@pytest.fixture
def mock_dependency_factory(mock_document_accessor, mock_content_accessor, mock_synthesis_engine):
    factory = Mock()
    factory.get_document_accessor.return_value = mock_document_accessor
    factory.get_content_accessor.return_value = mock_content_accessor
    factory.get_synthesis_engine.return_value = mock_synthesis_engine
    return factory


@pytest.fixture
def command(mock_dependency_factory):
    return ProcessDocumentChunksCreatedCommand(
        dependency_factory=mock_dependency_factory,
        document_id=123,
        job_id=456,
        receipt_handle="test-receipt",
        sqs_client=Mock(),
        sns_client=Mock(),
        logger=Mock(),
    )


class TestCrashRecovery:
    """Test crash recovery scenarios for workflow processors."""

    def test_cleanup_partial_called_on_failed_workflow(self, command, mock_document_accessor):
        """Test that _cleanup_partial is called when resuming a FAILED workflow."""
        # Setup: Create a failed workflow
        failed_workflow = MagicMock(spec=JobWorkflow)
        failed_workflow.status = JobWorkflowStatusEnum.FAILED
        failed_workflow.job_workflow_id = 123
        failed_workflow.run_count = 1
        mock_document_accessor.get_job_workflow.return_value = failed_workflow
        mock_document_accessor.reserve_next_pending.return_value = None  # No items to process

        # Mock the cleanup method to track calls
        with patch.object(command, '_cleanup_partial') as mock_cleanup:
            command.execute()

        # Verify cleanup was called
        mock_cleanup.assert_called_once()

    def test_cleanup_partial_processes_incomplete_items(self, command, mock_document_accessor, mock_content_accessor):
        """Test that _cleanup_partial processes all incomplete items."""
        # Setup: Mock incomplete items
        command.job_workflow_id = 123
        incomplete_items = ["1", "3", "5"]  # Chunks 1, 3, 5 failed
        mock_document_accessor.list_incomplete.return_value = incomplete_items

        # Execute cleanup
        command._cleanup_partial()

        # Verify cleanup was called for each incomplete item
        mock_document_accessor.list_incomplete.assert_called_once_with(123)
        assert mock_content_accessor.remove_chunk_statements.call_count == 3
        mock_content_accessor.remove_chunk_statements.assert_any_call(1)
        mock_content_accessor.remove_chunk_statements.assert_any_call(3)
        mock_content_accessor.remove_chunk_statements.assert_any_call(5)

    def test_workflow_resume_increments_run_count(self, command, mock_document_accessor):
        """Test that resuming a workflow increments the run count."""
        # Setup: Create an in-progress workflow
        in_progress_workflow = MagicMock(spec=JobWorkflow)
        in_progress_workflow.status = JobWorkflowStatusEnum.IN_PROGRESS
        in_progress_workflow.job_workflow_id = 123
        in_progress_workflow.run_count = 2
        mock_document_accessor.get_job_workflow.return_value = in_progress_workflow
        mock_document_accessor.reserve_next_pending.return_value = None  # No items to process

        # Execute
        command.execute()

        # Verify run count was incremented
        assert in_progress_workflow.run_count == 3
        mock_document_accessor.update_job_workflow.assert_any_call(
            in_progress_workflow, JobWorkflowStatusEnum.RETRYING
        )

    def test_failed_item_marked_correctly(self, command, mock_document_accessor, mock_synthesis_engine):
        """Test that failed items are marked correctly in the database."""
        # Setup: Create a workflow and mock item processing failure
        workflow = MagicMock(spec=JobWorkflow)
        workflow.status = JobWorkflowStatusEnum.STARTING
        workflow.job_workflow_id = 123
        mock_document_accessor.get_job_workflow.return_value = workflow
        mock_document_accessor.reserve_next_pending.side_effect = ["1", None]  # One item, then done

        # Mock synthesis engine to fail
        mock_synthesis_engine.generate_chunk_statements.side_effect = Exception("AI service unavailable")

        # Execute
        command.execute()

        # Verify the failed item was marked
        mock_document_accessor.mark_failed.assert_called_once_with(123, "1", "AI service unavailable")

    def test_partial_processing_state_cleanup(self, command, mock_document_accessor, mock_content_accessor):
        """Test cleanup of partial processing state after crash simulation."""
        # Setup: Simulate a crash scenario where some items are IN_PROGRESS
        command.job_workflow_id = 123

        # Mock database state after crash: some items completed, some in progress, some failed
        incomplete_items = ["2", "4"]  # Chunks 2 and 4 were in progress when crash occurred
        mock_document_accessor.list_incomplete.return_value = incomplete_items

        # Execute cleanup
        command._cleanup_partial()

        # Verify all incomplete items had their statements removed
        mock_content_accessor.remove_chunk_statements.assert_any_call(2)
        mock_content_accessor.remove_chunk_statements.assert_any_call(4)
        assert mock_content_accessor.remove_chunk_statements.call_count == 2

    def test_cleanup_item_removes_all_statements_and_subtopics(self, command, mock_content_accessor):
        """Test that cleanup_item removes both statements and subtopics for a chunk."""
        # Execute cleanup for a specific chunk
        command.cleanup_item("42")

        # Verify remove_chunk_statements was called (this removes ALL statements including subtopics)
        mock_content_accessor.remove_chunk_statements.assert_called_once_with(42)

    def test_crash_recovery_workflow_status_transitions(self, command, mock_document_accessor):
        """Test that workflow status transitions correctly during crash recovery."""
        # Setup: Create a failed workflow that needs recovery
        failed_workflow = MagicMock(spec=JobWorkflow)
        failed_workflow.status = JobWorkflowStatusEnum.FAILED
        failed_workflow.job_workflow_id = 123
        failed_workflow.run_count = 1
        mock_document_accessor.get_job_workflow.return_value = failed_workflow
        mock_document_accessor.list_incomplete.return_value = []  # No incomplete items for simplicity
        mock_document_accessor.reserve_next_pending.return_value = None  # No items to process

        # Execute recovery
        command.execute()

        # Verify status transitions: FAILED -> RETRYING -> IN_PROGRESS -> COMPLETED
        actual_calls = mock_document_accessor.update_job_workflow.call_args_list

        # Verify the key status transitions occurred
        assert len(actual_calls) >= 3, f"Expected at least 3 status updates, got {len(actual_calls)}"

        # Check that RETRYING status was set (crash recovery indicator)
        retrying_call_found = any(
            call[0][1] == JobWorkflowStatusEnum.RETRYING
            for call in actual_calls
        )
        assert retrying_call_found, "Expected workflow to be marked as RETRYING during recovery"

        # Check that run count was incremented
        assert failed_workflow.run_count == 2, f"Expected run count to be incremented to 2, got {failed_workflow.run_count}"
