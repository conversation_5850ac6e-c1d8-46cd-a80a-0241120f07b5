"""Integration tests for crash recovery functionality."""

import pytest
import time
from unittest.mock import Mock, patch
from sqlalchemy import text
from sqlalchemy.orm import Session

from manager.feed.commands.process_document_chunks_created_command import ProcessDocumentChunksCreatedCommand
from manager.feed.commands.dependency_factory import DependencyFactory
from shared.enums.job_enums import JobStatusEnum, JobItemTypeEnum
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum
from accessor.document.document_accessor import DocumentAccessor


# Test configuration
DOCUMENT_ID = 63510  # Document with chunks for testing
JOB_ID = 898765      # Job ID for testing
RECEIPT_HANDLE = "test-receipt-handle-crash-recovery"


class TestCrashRecoveryIntegration:
    """Integration tests for crash recovery with real database."""

    @classmethod
    def setup_class(cls):
        """Set up test dependencies."""
        cls.dependency_factory = DependencyFactory()
        cls.document_accessor = cls.dependency_factory.get_document_accessor()
        cls.content_accessor = cls.dependency_factory.get_content_accessor()
        cls.mock_sqs_client = Mock()
        cls.mock_sns_client = Mock()

    def create_test_job(self, document_id: int) -> int:
        """Create a test job for the given document."""
        with Session() as session:
            # Create a new job for testing
            result = session.execute(
                text("""
                    INSERT INTO job (reference_id, job_type_id, job_status_id, domain_id, created_at, updated_at)
                    SELECT :doc_id, job_type_id, :status_id, domain_id, NOW(), NOW()
                    FROM job
                    WHERE reference_id = :doc_id
                    LIMIT 1
                    RETURNING job_id
                """),
                {
                    "doc_id": document_id,
                    "status_id": JobStatusEnum.INITIAL_PROCESSING.value,
                }
            )
            job_id = result.scalar()
            session.commit()
            return job_id

    def cleanup_test_job(self, job_id: int):
        """Clean up test job and related data."""
        with Session() as session:
            try:
                # Delete job workflow items
                session.execute(
                    text("DELETE FROM job_item_workflow WHERE job_workflow_id IN (SELECT job_workflow_id FROM job_workflow WHERE job_id = :job_id)"),
                    {"job_id": job_id}
                )
                # Delete job workflows
                session.execute(
                    text("DELETE FROM job_workflow WHERE job_id = :job_id"),
                    {"job_id": job_id}
                )
                # Delete the job
                session.execute(
                    text("DELETE FROM job WHERE job_id = :job_id"),
                    {"job_id": job_id}
                )
                session.commit()
            except Exception as e:
                session.rollback()
                print(f"Warning: Error cleaning up test job {job_id}: {e}")

    def get_chunk_statement_count(self, document_id: int) -> int:
        """Get the count of statements for all chunks in a document."""
        with Session() as session:
            result = session.execute(
                text("""
                    SELECT COUNT(*)
                    FROM statement
                    WHERE chunk_id IN (
                        SELECT chunk_id
                        FROM chunk
                        WHERE document_id = :doc_id
                    )
                """),
                {"doc_id": document_id}
            )
            return result.scalar() or 0

    def get_workflow_status(self, job_id: int) -> tuple:
        """Get workflow status and item counts."""
        with Session() as session:
            # Get workflow status
            workflow_result = session.execute(
                text("SELECT status, run_count FROM job_workflow WHERE job_id = :job_id"),
                {"job_id": job_id}
            )
            workflow_row = workflow_result.first()

            if not workflow_row:
                return None, 0, 0, 0

            status = workflow_row[0]
            run_count = workflow_row[1]

            # Get item counts
            item_counts = session.execute(
                text("""
                    SELECT
                        SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as completed,
                        SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed,
                        SUM(CASE WHEN status IN ('STARTING', 'IN_PROGRESS') THEN 1 ELSE 0 END) as incomplete
                    FROM job_item_workflow jiw
                    JOIN job_workflow jw ON jiw.job_workflow_id = jw.job_workflow_id
                    WHERE jw.job_id = :job_id
                """),
                {"job_id": job_id}
            )
            counts = item_counts.first()
            completed = counts[0] or 0
            failed = counts[1] or 0
            incomplete = counts[2] or 0

            return status, run_count, completed, failed, incomplete

    def test_crash_recovery_simulation(self):
        """Test crash recovery by simulating a failure and recovery."""
        print(f"\n🧪 Testing crash recovery simulation for document {DOCUMENT_ID}")

        # Create a test job
        test_job_id = self.create_test_job(DOCUMENT_ID)
        print(f"📋 Created test job {test_job_id}")

        try:
            # Phase 1: Simulate initial processing with failure
            print("\n🚀 Phase 1: Initial processing with simulated crash")

            command = ProcessDocumentChunksCreatedCommand(
                dependency_factory=self.dependency_factory,
                document_id=DOCUMENT_ID,
                job_id=test_job_id,
                receipt_handle=RECEIPT_HANDLE,
                sqs_client=self.mock_sqs_client,
                sns_client=self.mock_sns_client,
            )

            # Mock synthesis engine to fail after processing some items
            original_generate = command.synthesis_engine.generate_chunk_statements
            call_count = 0

            def failing_generate(*args, **kwargs):
                nonlocal call_count
                call_count += 1
                if call_count <= 2:  # First 2 succeed
                    return original_generate(*args, **kwargs)
                else:  # 3rd and beyond fail (simulate crash)
                    raise Exception("Simulated pod crash")

            with patch.object(command.synthesis_engine, 'generate_chunk_statements', side_effect=failing_generate):
                try:
                    command.execute()
                    assert False, "Expected command to fail due to simulated crash"
                except Exception as e:
                    print(f"✅ Expected failure occurred: {e}")

            # Check state after "crash"
            status, run_count, completed, failed, incomplete = self.get_workflow_status(test_job_id)
            print(f"📊 After crash - Status: {status}, Run: {run_count}, Completed: {completed}, Failed: {failed}, Incomplete: {incomplete}")

            assert status == JobWorkflowStatusEnum.FAILED.value
            assert failed > 0, "Should have failed items after crash"

            # Phase 2: Recovery attempt
            print("\n🔄 Phase 2: Recovery attempt")

            # Create new command for recovery
            recovery_command = ProcessDocumentChunksCreatedCommand(
                dependency_factory=self.dependency_factory,
                document_id=DOCUMENT_ID,
                job_id=test_job_id,
                receipt_handle=RECEIPT_HANDLE + "-recovery",
                sqs_client=self.mock_sqs_client,
                sns_client=self.mock_sns_client,
            )

            # Record statements before recovery
            statements_before = self.get_chunk_statement_count(DOCUMENT_ID)
            print(f"📝 Statements before recovery: {statements_before}")

            # Execute recovery (should clean up and retry)
            recovery_command.execute()

            # Check state after recovery
            status, run_count, completed, failed, incomplete = self.get_workflow_status(test_job_id)
            statements_after = self.get_chunk_statement_count(DOCUMENT_ID)

            print(f"📊 After recovery - Status: {status}, Run: {run_count}, Completed: {completed}, Failed: {failed}, Incomplete: {incomplete}")
            print(f"📝 Statements after recovery: {statements_after}")

            # Verify recovery was successful
            assert status == JobWorkflowStatusEnum.COMPLETED.value, f"Expected COMPLETED, got {status}"
            assert run_count >= 2, f"Expected run count >= 2, got {run_count}"
            assert incomplete == 0, f"Expected no incomplete items, got {incomplete}"
            assert statements_after > statements_before, "Should have more statements after recovery"

            print("✅ Crash recovery test completed successfully!")

        finally:
            # Clean up
            self.cleanup_test_job(test_job_id)
            print(f"🧹 Cleaned up test job {test_job_id}")

    def test_cleanup_partial_state(self):
        """Test that partial state is properly cleaned up."""
        print(f"\n🧪 Testing partial state cleanup for document {DOCUMENT_ID}")

        # Create a test job
        test_job_id = self.create_test_job(DOCUMENT_ID)
        print(f"📋 Created test job {test_job_id}")

        try:
            # Create command and manually create workflow with failed items
            command = ProcessDocumentChunksCreatedCommand(
                dependency_factory=self.dependency_factory,
                document_id=DOCUMENT_ID,
                job_id=test_job_id,
                receipt_handle=RECEIPT_HANDLE,
                sqs_client=self.mock_sqs_client,
                sns_client=self.mock_sns_client,
            )

            # Manually create workflow and items to simulate crash state
            workflow, _ = command._get_or_create_job_workflow()
            command._seed_item_rows()

            # Manually mark some items as failed to simulate crash
            with Session() as session:
                session.execute(
                    text("""
                        UPDATE job_item_workflow
                        SET status = 'FAILED', last_error = 'Simulated crash'
                        WHERE job_workflow_id = :workflow_id
                        AND item_key IN ('1', '3')
                    """),
                    {"workflow_id": workflow.job_workflow_id}
                )
                session.commit()

            # Manually add some statements to simulate partial processing
            chunk_ids = self.document_accessor.get_chunk_ids_for_document(DOCUMENT_ID)
            if len(chunk_ids) >= 2:
                # Add statements for first 2 chunks to simulate partial processing
                for chunk_id in chunk_ids[:2]:
                    self.content_accessor.save_statement({
                        "chunk_id": chunk_id,
                        "statement_type_id": 1,  # REQUIREMENT
                        "text": f"Test statement for chunk {chunk_id}"
                    })

            statements_before = self.get_chunk_statement_count(DOCUMENT_ID)
            print(f"📝 Statements before cleanup: {statements_before}")

            # Execute cleanup
            command._cleanup_partial()

            statements_after = self.get_chunk_statement_count(DOCUMENT_ID)
            print(f"📝 Statements after cleanup: {statements_after}")

            # Verify cleanup removed statements for failed chunks
            assert statements_after < statements_before, "Cleanup should have removed some statements"

            print("✅ Partial state cleanup test completed successfully!")

        finally:
            # Clean up
            self.cleanup_test_job(test_job_id)
            print(f"🧹 Cleaned up test job {test_job_id}")

    def test_concurrent_crash_recovery(self):
        """Test crash recovery in a concurrent processing scenario."""
        print(f"\n🧪 Testing concurrent crash recovery for document {DOCUMENT_ID}")

        # Create a test job
        test_job_id = self.create_test_job(DOCUMENT_ID)
        print(f"📋 Created test job {test_job_id}")

        try:
            # This test simulates what happens when multiple threads are processing
            # and the pod crashes, leaving items in IN_PROGRESS state

            command = ProcessDocumentChunksCreatedCommand(
                dependency_factory=self.dependency_factory,
                document_id=DOCUMENT_ID,
                job_id=test_job_id,
                receipt_handle=RECEIPT_HANDLE,
                sqs_client=self.mock_sqs_client,
                sns_client=self.mock_sns_client,
            )

            # Create workflow and seed items
            workflow, _ = command._get_or_create_job_workflow()
            command._seed_item_rows()

            # Simulate concurrent processing crash by manually setting items to IN_PROGRESS
            with Session() as session:
                session.execute(
                    text("""
                        UPDATE job_item_workflow
                        SET status = 'IN_PROGRESS', run_count = 1
                        WHERE job_workflow_id = :workflow_id
                        AND item_key IN ('2', '4', '5')
                    """),
                    {"workflow_id": workflow.job_workflow_id}
                )

                # Mark workflow as failed to simulate crash during processing
                session.execute(
                    text("UPDATE job_workflow SET status = 'FAILED' WHERE job_workflow_id = :workflow_id"),
                    {"workflow_id": workflow.job_workflow_id}
                )
                session.commit()

            # Add partial statements to simulate incomplete processing
            chunk_ids = self.document_accessor.get_chunk_ids_for_document(DOCUMENT_ID)
            if len(chunk_ids) >= 5:
                # Add statements for chunks 2, 4, 5 to simulate partial processing
                for i, chunk_id in enumerate([chunk_ids[1], chunk_ids[3], chunk_ids[4]]):
                    self.content_accessor.save_statement({
                        "chunk_id": chunk_id,
                        "statement_type_id": 1,
                        "text": f"Partial statement for chunk {chunk_id}"
                    })

            statements_before = self.get_chunk_statement_count(DOCUMENT_ID)
            status_before, run_count_before, completed_before, failed_before, incomplete_before = self.get_workflow_status(test_job_id)

            print(f"📊 Before recovery - Status: {status_before}, Incomplete: {incomplete_before}, Statements: {statements_before}")

            # Execute recovery
            recovery_command = ProcessDocumentChunksCreatedCommand(
                dependency_factory=self.dependency_factory,
                document_id=DOCUMENT_ID,
                job_id=test_job_id,
                receipt_handle=RECEIPT_HANDLE + "-concurrent-recovery",
                sqs_client=self.mock_sqs_client,
                sns_client=self.mock_sns_client,
            )

            recovery_command.execute()

            # Check final state
            status_after, run_count_after, completed_after, failed_after, incomplete_after = self.get_workflow_status(test_job_id)
            statements_after = self.get_chunk_statement_count(DOCUMENT_ID)

            print(f"📊 After recovery - Status: {status_after}, Run: {run_count_after}, Completed: {completed_after}, Incomplete: {incomplete_after}")
            print(f"📝 Statements after recovery: {statements_after}")

            # Verify recovery was successful
            assert status_after == JobWorkflowStatusEnum.COMPLETED.value, f"Expected COMPLETED, got {status_after}"
            assert run_count_after > run_count_before, "Run count should have incremented"
            assert incomplete_after == 0, "Should have no incomplete items after recovery"
            assert statements_after > statements_before, "Should have generated more statements"

            print("✅ Concurrent crash recovery test completed successfully!")

        finally:
            # Clean up
            self.cleanup_test_job(test_job_id)
            print(f"🧹 Cleaned up test job {test_job_id}")
