"""Integration tests for ProcessDocumentChunksCreatedCommand following the manual test pattern.

This test follows the same pattern as test_feed_manager.py:
1. Run test_01_setup_chunks to generate chunks (like PROCESS_DOCUMENT_SUBMITTED)
2. Run test_02_process_document_chunks_created to test the command
3. Run test_03_concurrent_processing to test multithreading

Run tests individually in order, not as a full suite.
"""

import logging
import os
import threading
import time
import unittest
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import Mock, MagicMock

from sqlalchemy import text

from manager.feed.commands.command_factory import CommandFactory
from manager.feed.commands.command_handler import CommandHandler
from manager.feed.commands.dependency_factory import DependencyFactory
from shared.enums.job_enums import JobStatusEnum, MessageType
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum
from shared.models.workflow_type_enum import WorkflowTypeEnum
from util.audit.user_ctx import UserCtx, set_user_ctx
from util.database import Session


# Static variables - use a known document/job that has chunks
DOCUMENT_ID = 1044  # Known document with chunks
JOB_ID = 1012       # Known job for this document
RECEIPT_HANDLE = "test-receipt-handle"

# File to store test document/job IDs between test runs
TEST_DATA_FILE = "/tmp/phoenix_burst_integration_test_data.txt"


def save_test_document_job(doc_id, job_id):
    """Save document and job IDs to a file for use between tests."""
    try:
        with open(TEST_DATA_FILE, 'w') as f:
            f.write(f"{doc_id},{job_id}")
        print(f"💾 Saved test data: document {doc_id}, job {job_id}")
    except Exception as e:
        print(f"⚠️  Failed to save test data: {e}")


def load_test_document_job():
    """Load document and job IDs from file, or return defaults."""
    try:
        if os.path.exists(TEST_DATA_FILE):
            with open(TEST_DATA_FILE, 'r') as f:
                content = f.read().strip()
                if content and ',' in content:
                    doc_id, job_id = content.split(',')
                    return int(doc_id), int(job_id)
    except Exception as e:
        print(f"⚠️  Failed to load test data: {e}")

    return DOCUMENT_ID, JOB_ID


class TestProcessDocumentChunksCreatedCommandIntegration(unittest.TestCase):
    """Integration tests following the manual test pattern from test_feed_manager.py."""

    # Class variables for dynamic document/job assignment
    test_document_id = DOCUMENT_ID
    test_job_id = JOB_ID

    @classmethod
    def setUpClass(cls):
        """Set up test environment once for all tests."""
        cls.logger = logging.getLogger("integration_test")
        cls.logger.setLevel(logging.DEBUG)

        # Create handler if not exists
        if not cls.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            cls.logger.addHandler(handler)

        # Reset document job state for clean testing
        cls.reset_document_job_state(DOCUMENT_ID, JOB_ID)

        # Set up mocked AWS clients
        cls.mock_sqs_client = MagicMock()
        cls.mock_sns_client = MagicMock()
        cls.mock_sns_client.publish.return_value = {
            "ResponseMetadata": {"HTTPStatusCode": 200},
            "MessageId": str(uuid.uuid4()),
        }
        cls.mock_sns_client.get_topic_arn.return_value = "arn:aws:sns:us-east-1:123456789012:test-topic"

        # Set up command infrastructure
        cls.dependency_factory = DependencyFactory("integration_test")
        cls.command_factory = CommandFactory(cls.dependency_factory, cls.logger)
        cls.command_handler = CommandHandler(cls.logger)
        cls.document_accessor = cls.dependency_factory.get_document_accessor()

        # Set up system user context for auditing
        cls.setup_system_user_context()

        # Patch the auditor to handle None users gracefully in tests
        cls.patch_auditor_for_tests()

    @classmethod
    def setup_system_user_context(cls):
        """Set up system user context for audit logging in tests."""
        try:
            from accessor.identity.accessors.user_repository import UserRepository
            user_repo = UserRepository(Session)
            system_user = user_repo.get_system_user()

            if system_user and system_user.enterprise:
                user_ctx = UserCtx(
                    auth0_id=system_user.auth0_id,
                    user_id=system_user.user_id,
                    email=system_user.email_address,
                    name=f"{system_user.first_name} {system_user.last_name}",
                    enterprise_id=system_user.enterprise_id,
                    domain_id=system_user.enterprise.domain.domain_id,
                )
                set_user_ctx(user_ctx)
                print(f"✅ Set system user context: {system_user.email_address}")
            else:
                # Fallback: create Burst System user context
                user_ctx = UserCtx(
                    auth0_id="burst-system-user",
                    user_id=35,
                    email="<EMAIL>",
                    name="Burst System",
                    enterprise_id=1,
                    domain_id=1,
                )
                set_user_ctx(user_ctx)
                print("⚠️  Using fallback Burst System user context")
        except Exception as e:
            print(f"❌ Error setting up user context: {e}")
            # Create Burst System fallback context
            user_ctx = UserCtx(
                auth0_id="burst-system-user",
                user_id=35,
                email="<EMAIL>",
                name="Burst System",
                enterprise_id=1,
                domain_id=1,
            )
            set_user_ctx(user_ctx)
            print("⚠️  Using Burst System fallback user context")

    @classmethod
    def patch_auditor_for_tests(cls):
        """Patch the auditor to handle None users gracefully in tests."""
        from util.audit.auditor import Auditor

        def patched_record(self, dto):
            """Patched record method that handles None users gracefully."""
            from util.audit.user_ctx import get_user_ctx
            from shared.models import AuditActivity

            user = get_user_ctx()

            # Handle None user gracefully for tests - use Burst System user
            user_id = user.user_id if user else 35  # Burst System user_id
            email = getattr(user, "email", None) or getattr(user, "email_address", "<EMAIL>") if user else "<EMAIL>"

            print(f"🔍 Recording audit activity | object={dto.object_key} | operation={dto.operation_type} | user={email}")

            entry = AuditActivity(
                audit_object_type_id=dto.object_type.value,
                object_key=dto.object_key,
                data_snapshot=dto.snapshot,
                user_id=user_id,  # This can be None
                audit_operation_type_id=dto.operation_type.value,
            )

            self._save_audit_activity(entry)

        # Apply the patch
        Auditor.record = patched_record
        print("✅ Patched auditor to handle None users in tests")

    @classmethod
    def reset_document_job_state(cls, doc_id, job_id):
        """Reset document job state in the database for clean testing."""
        with Session() as session:
            try:
                # Delete statements for all chunks in this document
                session.execute(
                    text("""
                        DELETE FROM statement
                        WHERE chunk_id IN (
                            SELECT chunk_id
                            FROM chunk
                            WHERE document_id = :doc_id
                        )
                    """),
                    {"doc_id": doc_id},
                )

                # Set job status to initial processing
                session.execute(
                    text("UPDATE job SET job_status_id = :status_id WHERE reference_id = :doc_id"),
                    {
                        "status_id": JobStatusEnum.INITIAL_PROCESSING.value,
                        "doc_id": doc_id,
                    },
                )

                # Delete existing job workflows for this job to force fresh start
                session.execute(
                    text("DELETE FROM job_item_workflow WHERE job_workflow_id IN (SELECT job_workflow_id FROM job_workflow WHERE job_id = :job_id)"),
                    {"job_id": job_id},
                )
                session.execute(
                    text("DELETE FROM job_workflow WHERE job_id = :job_id"),
                    {"job_id": job_id},
                )

                session.commit()
                print(f"✅ Reset state for document {doc_id}, job {job_id}")
                print("   - Deleted statements for document chunks")
                print("   - Deleted existing workflows and workflow items")
                print("   - Preserved chunks for testing")

            except Exception as e:
                session.rollback()
                print(f"❌ Error resetting state: {e}")
                raise e

    @classmethod
    def create_test_job(cls, doc_id, job_type_id=5):
        """Create a new job for testing purposes."""
        with Session() as session:
            try:
                from shared.models.job import Job

                # Create a new job
                new_job = Job(
                    reference_id=doc_id,
                    job_status_id=JobStatusEnum.INITIAL_PROCESSING.value,
                    job_type_id=job_type_id,
                    global_knowledge=False,
                )
                session.add(new_job)
                session.commit()
                session.refresh(new_job)

                print(f"✅ Created test job {new_job.job_id} for document {doc_id}")
                return new_job.job_id

            except Exception as e:
                session.rollback()
                print(f"❌ Error creating test job: {e}")
                raise e



    def test_01_setup_chunks(self):
        """Step 1: Find a document with chunks or generate them.

        This test finds a document that already has chunks, or provides
        instructions for generating them.
        """
        print("\n🔧 Setting up chunks for testing")

        # First, check if the configured document has chunks
        chunk_ids = self.document_accessor.get_chunk_ids_for_document(DOCUMENT_ID)
        print(f"📊 Document {DOCUMENT_ID} currently has {len(chunk_ids)} chunks")

        if len(chunk_ids) >= 3:
            print(f"✅ Document {DOCUMENT_ID} already has sufficient chunks ({len(chunk_ids)})")
            print("🎯 Ready for test_02_process_document_chunks_created")
            return

        # If configured document doesn't have chunks, find one that does
        print("🔍 Searching for documents with chunks...")
        jobs = self.document_accessor.get_jobs(limit=20)

        for job in jobs:
            # Skip documents with domain_id 33 (as requested)
            if job.domain_id == 33:
                print(f"⏭️  Skipping document {job.reference_id} (domain_id 33)")
                continue

            job_chunk_ids = self.document_accessor.get_chunk_ids_for_document(job.reference_id)
            if len(job_chunk_ids) >= 3:
                print(f"✅ Found document {job.reference_id} with {len(job_chunk_ids)} chunks (domain_id: {job.domain_id})")
                print(f"💡 Update DOCUMENT_ID to {job.reference_id} in the test file")
                print(f"   Also update JOB_ID to {job.job_id}")
                print("🎯 Then run test_02_process_document_chunks_created")

                # Save the found document/job for other tests to use
                save_test_document_job(job.reference_id, job.job_id)

                print(f"🔄 Using document {job.reference_id}, job {job.job_id} for subsequent tests")
                print(f"   (Saved to {TEST_DATA_FILE})")
                return

        # If no documents with chunks found, provide instructions
        print("⚠️  No documents with chunks found. You need to:")
        print("   1. Run PROCESS_DOCUMENT_SUBMITTED command first to generate chunks")
        print("   2. Or upload a document and process it")
        print("   3. Then update DOCUMENT_ID and JOB_ID in this test file")
        print(f"   4. Current document {DOCUMENT_ID} has {len(chunk_ids)} chunks")

        # Don't fail the test, just skip it
        self.skipTest("No documents with chunks found. Generate chunks first.")

    def test_02_process_document_chunks_created(self):
        """Step 2: Test the ProcessDocumentChunksCreatedCommand with real database.

        This is the main test - it exercises the command with real database connections,
        workflow creation, item seeding, and statement generation.
        """
        print(f"\n🚀 Testing ProcessDocumentChunksCreatedCommand for document {DOCUMENT_ID}")

        # Load the document/job that was found in test_01 or use defaults
        doc_id, job_id = load_test_document_job()

        # Reset the job and workflow state for clean testing (includes clearing statements)
        self.reset_document_job_state(doc_id, job_id)

        # Create the command using the command factory (like the real system)
        command = self.command_factory.get_command(
            MessageType.PROCESS_DOCUMENT_CHUNKS_CREATED.value,
            doc_id,
            job_id,
            RECEIPT_HANDLE,
            self.mock_sqs_client,
            self.mock_sns_client,
        )

        print(f"📋 Created command: {type(command).__name__}")
        print(f"🎯 Workflow type: {command.workflow_type}")

        # Check initial state
        chunk_ids = self.document_accessor.get_chunk_ids_for_document(doc_id)
        print(f"📊 Document {doc_id} has {len(chunk_ids)} chunks to process")

        # Execute the command (this will create workflow, seed items, process chunks)
        print("⚡ Executing command...")
        self.command_handler.handle(command)

        # Verify results
        print("✅ Command executed successfully!")
        print(f"🔄 Workflow ID: {command.job_workflow_id}")

        # Check that items were processed
        if hasattr(command, 'job_workflow_id') and command.job_workflow_id:
            incomplete_items = self.document_accessor.list_incomplete_items(
                command.job_workflow_id, limit=10
            )
            print(f"📈 Remaining incomplete items: {len(incomplete_items)}")

            # Check if statements were generated (this would be the real test)
            with Session() as session:
                statement_count = session.execute(
                    text("""
                        SELECT COUNT(*)
                        FROM statement
                        WHERE chunk_id IN (
                            SELECT chunk_id
                            FROM chunk
                            WHERE document_id = :doc_id
                        )
                    """),
                    {"doc_id": doc_id}
                ).scalar()
                print(f"📝 Generated {statement_count} statements")

        print("🎉 Test completed successfully!")

    def test_03_concurrent_processing(self):
        """Step 3: Test concurrent processing with multiple threads.

        This test creates a workflow and then processes items concurrently
        to test thread safety and database locking.
        """
        # Load the document/job that was found in test_01 or use defaults
        doc_id, _ = load_test_document_job()
        print(f"\n🔀 Testing concurrent processing for document {doc_id}")

        # Create a new job for concurrency testing to avoid conflicts
        concurrent_job_id = self.create_test_job(doc_id)

        # Create a fresh workflow for concurrency testing
        command = self.command_factory.get_command(
            MessageType.PROCESS_DOCUMENT_CHUNKS_CREATED.value,
            doc_id,
            concurrent_job_id,
            RECEIPT_HANDLE,
            self.mock_sqs_client,
            self.mock_sns_client,
        )

        # Mock the synthesis engine to avoid actual AI calls
        original_get_synthesis_engine = command.dependency_factory.get_synthesis_engine
        mock_synthesis_engine = Mock()
        command.dependency_factory.get_synthesis_engine = lambda: mock_synthesis_engine

        try:
            # Create workflow and seed items (but don't process them yet)
            print("🔧 Setting up workflow and seeding items...")
            command.execute()  # This will seed items but we'll process them manually

            workflow_id = command.job_workflow_id
            print(f"🔄 Created workflow {workflow_id}")

            # Get items to process concurrently
            items = self.document_accessor.list_incomplete_items(workflow_id, limit=5)
            print(f"📊 Found {len(items)} items for concurrent processing")

            if len(items) < 2:
                print("⚠️  Need at least 2 items for concurrency test")
                return

            # Track results
            results = []
            errors = []

            def process_item_thread(item_key):
                """Process a single item in a thread."""
                thread_id = threading.current_thread().ident
                try:
                    print(f"🧵 Thread {thread_id} processing item {item_key}")

                    # Try to reserve the item (atomic operation)
                    reserved = self.document_accessor.reserve_item(workflow_id, item_key)
                    if reserved:
                        print(f"🔒 Thread {thread_id} reserved item {item_key}")

                        # Simulate processing work
                        time.sleep(0.1)

                        # Mark as done
                        self.document_accessor.mark_item_done(workflow_id, item_key)
                        results.append((thread_id, item_key, "completed"))
                        print(f"✅ Thread {thread_id} completed item {item_key}")
                    else:
                        results.append((thread_id, item_key, "already_reserved"))
                        print(f"⏭️  Thread {thread_id} - item {item_key} already reserved")

                except Exception as e:
                    error_msg = f"Thread {thread_id} failed on item {item_key}: {e}"
                    errors.append(error_msg)
                    print(f"❌ {error_msg}")

            # Process items concurrently
            print(f"🚀 Starting {min(3, len(items))} concurrent threads...")
            with ThreadPoolExecutor(max_workers=3) as executor:
                futures = [
                    executor.submit(process_item_thread, item.item_key)
                    for item in items[:3]
                ]

                # Wait for completion
                for future in as_completed(futures):
                    future.result()

            # Report results
            print("📊 Processing complete:")
            print(f"   ✅ Successful operations: {len([r for r in results if r[2] == 'completed'])}")
            print(f"   ⏭️  Already reserved: {len([r for r in results if r[2] == 'already_reserved'])}")
            print(f"   ❌ Errors: {len(errors)}")

            if errors:
                for error in errors:
                    print(f"   🔍 {error}")

            # Verify no errors occurred
            self.assertEqual(len(errors), 0, f"Concurrency errors: {errors}")
            print("🎉 Concurrent processing test completed successfully!")

        finally:
            # Cleanup
            command.dependency_factory.get_synthesis_engine = original_get_synthesis_engine
            if hasattr(command, 'job_workflow_id') and command.job_workflow_id:
                try:
                    self.document_accessor.mark_workflow_failed(command.job_workflow_id)
                    print(f"🧹 Cleaned up workflow {command.job_workflow_id}")
                except Exception as e:
                    print(f"⚠️  Cleanup warning: {e}")

    def test_04_database_verification(self):
        """Step 4: Verify database state and connections.

        This test verifies that we can connect to the database and
        retrieve the expected data structures.
        """
        # Load the document/job that was found in test_01 or use defaults
        doc_id, _ = load_test_document_job()
        print(f"\n🔍 Verifying database connections and data for document {doc_id}")

        # Test chunk retrieval
        chunk_ids = self.document_accessor.get_chunk_ids_for_document(doc_id)
        print(f"📊 Document {doc_id} has {len(chunk_ids)} chunks")
        self.assertIsInstance(chunk_ids, list)

        # Test job type retrieval
        job_type_id = self.document_accessor.get_job_type_id_by_document(doc_id)
        print(f"🏷️  Document {doc_id} has job_type_id: {job_type_id}")
        if job_type_id is not None:
            self.assertIsInstance(job_type_id, int)
        else:
            print(f"⚠️  Document {doc_id} has no associated job - this is expected for some test documents")

        # Test job retrieval
        jobs = self.document_accessor.get_jobs(limit=5)
        print(f"📋 Found {len(jobs)} jobs in database")
        self.assertGreater(len(jobs), 0)

        # Test workflow type enum
        from manager.feed.commands.process_document_chunks_created_command import ProcessDocumentChunksCreatedCommand
        temp_command = ProcessDocumentChunksCreatedCommand(
            dependency_factory=self.dependency_factory,
            document_id=doc_id,
            job_id=999999,  # Use a dummy job ID for this test
            receipt_handle=RECEIPT_HANDLE,
            sqs_client=self.mock_sqs_client,
            sns_client=self.mock_sns_client,
            logger=self.logger,
        )
        print(f"🎯 Command workflow type: {temp_command.workflow_type}")
        self.assertEqual(temp_command.workflow_type, WorkflowTypeEnum.GENERATE_STATEMENTS)

        print("✅ Database verification completed successfully!")


if __name__ == "__main__":
    # Print instructions for manual testing
    print("=" * 80)
    print("🧪 INTEGRATION TEST INSTRUCTIONS")
    print("=" * 80)
    print("This test follows the manual pattern from test_feed_manager.py")
    print()
    print("Run tests individually in this order:")
    print("1. test_01_setup_chunks        - Verify chunks exist")
    print("2. test_02_process_document_chunks_created - Main test")
    print("3. test_03_concurrent_processing - Test multithreading")
    print("4. test_04_database_verification - Verify connections")
    print()
    print("Example:")
    print("  python -m pytest test/integration/manager/feed/commands/test_process_document_chunks_created_command_integration.py::TestProcessDocumentChunksCreatedCommandIntegration::test_01_setup_chunks -v -s")
    print("=" * 80)

    unittest.main()
