"""True integration tests for ProcessDocumentChunksCreatedCommand with database and multithreading."""

import logging
import threading
import time
import unittest
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import Mock, patch

from manager.feed.commands.dependency_factory import DependencyFactory
from manager.feed.commands.process_document_chunks_created_command import ProcessDocumentChunksCreatedCommand
from shared.enums.job_enums import JobItemTypeEnum
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum
from shared.models.workflow_type_enum import WorkflowTypeEnum
from util.config import AWS_FEED_MANAGER_ACCESS_KEY, AWS_FEED_MANAGER_SECRET_KEY


class TestProcessDocumentChunksCreatedCommandIntegration(unittest.TestCase):
    """Integration tests with real database connections and multithreading."""

    @classmethod
    def setUpClass(cls):
        """Set up test environment once for all tests."""
        cls.logger = logging.getLogger("integration_test")
        cls.logger.setLevel(logging.DEBUG)

        # Create handler if not exists
        if not cls.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            cls.logger.addHandler(handler)

    def setUp(self):
        """Set up each test."""
        self.dependency_factory = DependencyFactory(
            logger_name="integration_test"
        )

        # Mock SNS and SQS clients to avoid actual AWS calls
        self.mock_sqs_client = Mock()
        self.mock_sns_client = Mock()
        self.mock_sns_client.get_topic_arn.return_value = "arn:aws:sns:us-east-1:123456789012:test-topic"
        self.mock_sns_client.publish.return_value = {
            "ResponseMetadata": {"HTTPStatusCode": 200},
            "MessageId": "test-message-id"
        }

        # Get real document accessor for database operations
        self.document_accessor = self.dependency_factory.get_document_accessor()

        # Find a real document with chunks for testing
        self.test_document_id = self._find_test_document()
        self.test_job_id = 999999  # Use a high number to avoid conflicts

    def _find_test_document(self):
        """Find a document that has chunks for testing."""
        try:
            # Get a document that has chunks
            jobs = self.document_accessor.get_jobs(limit=10)
            for job in jobs:
                chunk_ids = self.document_accessor.get_chunk_ids_for_document(job.document_id)
                if chunk_ids and len(chunk_ids) >= 3:  # Need at least 3 chunks for testing
                    self.logger.info(f"Found test document {job.document_id} with {len(chunk_ids)} chunks")
                    return job.document_id

            # If no suitable document found, use the first available
            if jobs:
                self.logger.warning(f"Using document {jobs[0].document_id} even though it may not have chunks")
                return jobs[0].document_id

            raise RuntimeError("No documents found in database for testing")

        except Exception as e:
            self.logger.error(f"Error finding test document: {e}")
            # Fallback to a known document ID
            return 1

    def _create_command(self, document_id=None, job_id=None):
        """Create a command instance with real dependencies."""
        return ProcessDocumentChunksCreatedCommand(
            dependency_factory=self.dependency_factory,
            document_id=document_id or self.test_document_id,
            job_id=job_id or self.test_job_id,
            receipt_handle="test-receipt-handle",
            sqs_client=self.mock_sqs_client,
            sns_client=self.mock_sns_client,
            logger=self.logger,
        )

    def test_database_connection_and_chunk_retrieval(self):
        """Test that command can connect to database and retrieve chunks."""
        # Test that we can get chunk IDs from the database
        chunk_ids = self.document_accessor.get_chunk_ids_for_document(self.test_document_id)
        self.logger.info(f"Retrieved {len(chunk_ids)} chunks for document {self.test_document_id}")

        # Test that we can get job type ID
        job_type_id = self.document_accessor.get_job_type_id_by_document(self.test_document_id)
        self.logger.info(f"Retrieved job type ID {job_type_id} for document {self.test_document_id}")

        self.assertIsInstance(chunk_ids, list)
        self.assertIsInstance(job_type_id, int)

    def test_workflow_creation_and_seeding(self):
        """Test creating workflow and seeding items in database."""
        command = self._create_command()

        # Mock synthesis engine to avoid actual AI calls
        with patch.object(command.dependency_factory, 'get_synthesis_engine') as mock_get_engine:
            mock_synthesis_engine = Mock()
            mock_get_engine.return_value = mock_synthesis_engine

            try:
                # Execute the workflow creation and seeding
                command.execute()

                # Verify workflow was created in database
                self.assertIsNotNone(command.job_workflow_id)
                self.logger.info(f"Created workflow with ID: {command.job_workflow_id}")

                # Verify items were seeded
                incomplete_items = self.document_accessor.list_incomplete_items(
                    command.job_workflow_id,
                    limit=100
                )
                self.logger.info(f"Found {len(incomplete_items)} incomplete items")
                self.assertGreater(len(incomplete_items), 0, "Should have seeded some items")

                # Verify item types are correct
                for item in incomplete_items:
                    self.assertEqual(item.item_type, JobItemTypeEnum.CHUNK.value)

            finally:
                # Cleanup: mark workflow as failed to clean up test data
                if hasattr(command, 'job_workflow_id') and command.job_workflow_id:
                    try:
                        self.document_accessor.mark_workflow_failed(command.job_workflow_id)
                        self.logger.info(f"Cleaned up workflow {command.job_workflow_id}")
                    except Exception as e:
                        self.logger.warning(f"Failed to cleanup workflow: {e}")

    def test_concurrent_item_processing(self):
        """Test processing multiple items concurrently with thread safety."""
        command = self._create_command()

        # Mock synthesis engine to simulate processing
        with patch.object(command.dependency_factory, 'get_synthesis_engine') as mock_get_engine:
            mock_synthesis_engine = Mock()
            mock_get_engine.return_value = mock_synthesis_engine

            # Create a workflow and seed items
            try:
                command.execute()
                workflow_id = command.job_workflow_id

                # Get some items to process
                items = self.document_accessor.list_incomplete_items(workflow_id, limit=5)
                if not items:
                    self.skipTest("No items available for concurrent processing test")

                self.logger.info(f"Testing concurrent processing of {len(items)} items")

                # Track processing results
                results = []
                errors = []

                def process_item_thread(item_id):
                    """Process a single item in a thread."""
                    try:
                        thread_id = threading.current_thread().ident
                        self.logger.info(f"Thread {thread_id} processing item {item_id}")

                        # Reserve the item
                        reserved = self.document_accessor.reserve_item(workflow_id, item_id)
                        if reserved:
                            # Simulate processing
                            time.sleep(0.1)  # Small delay to simulate work

                            # Mark as done
                            self.document_accessor.mark_item_done(workflow_id, item_id)
                            results.append((thread_id, item_id, "success"))
                            self.logger.info(f"Thread {thread_id} completed item {item_id}")
                        else:
                            results.append((thread_id, item_id, "already_reserved"))

                    except Exception as e:
                        errors.append((threading.current_thread().ident, item_id, str(e)))
                        self.logger.error(f"Thread {threading.current_thread().ident} failed on item {item_id}: {e}")

                # Process items concurrently
                with ThreadPoolExecutor(max_workers=3) as executor:
                    futures = [
                        executor.submit(process_item_thread, item.item_id)
                        for item in items[:3]  # Process first 3 items
                    ]

                    # Wait for all threads to complete
                    for future in as_completed(futures):
                        future.result()  # This will raise any exceptions

                # Verify results
                self.assertEqual(len(errors), 0, f"Errors occurred: {errors}")
                self.assertGreater(len(results), 0, "Should have processed some items")

                # Verify database state
                remaining_items = self.document_accessor.list_incomplete_items(workflow_id, limit=100)
                completed_count = len(items) - len(remaining_items)
                self.logger.info(f"Completed {completed_count} items, {len(remaining_items)} remaining")

            finally:
                # Cleanup
                if hasattr(command, 'job_workflow_id') and command.job_workflow_id:
                    try:
                        self.document_accessor.mark_workflow_failed(command.job_workflow_id)
                    except Exception as e:
                        self.logger.warning(f"Failed to cleanup workflow: {e}")

    def test_workflow_type_property(self):
        """Test workflow type property returns correct enum."""
        command = self._create_command()
        self.assertEqual(command.workflow_type, WorkflowTypeEnum.GENERATE_STATEMENTS)

    def test_command_with_different_documents(self):
        """Test command works with different document IDs."""
        # Get multiple documents for testing
        jobs = self.document_accessor.get_jobs(limit=3)

        if len(jobs) < 2:
            self.skipTest("Need at least 2 documents for this test")

        for i, job in enumerate(jobs[:2]):
            with self.subTest(document_id=job.document_id):
                # Test that command can handle different documents
                chunk_ids = self.document_accessor.get_chunk_ids_for_document(job.document_id)
                job_type_id = self.document_accessor.get_job_type_id_by_document(job.document_id)

                self.assertIsInstance(chunk_ids, list)
                self.assertIsInstance(job_type_id, int)
                self.logger.info(f"Document {job.document_id}: {len(chunk_ids)} chunks, job_type_id={job_type_id}")


if __name__ == "__main__":
    unittest.main()
